{"version": 3, "names": ["_index", "require", "BLOCK_SCOPED_SYMBOL", "Symbol", "for", "isLet", "node", "isVariableDeclaration", "kind"], "sources": ["../../src/validators/isLet.ts"], "sourcesContent": ["import { isVariableDeclaration } from \"./generated/index.ts\";\nimport type * as t from \"../index.ts\";\n\nif (!process.env.BABEL_8_BREAKING) {\n  // eslint-disable-next-line no-var\n  var BLOCK_SCOPED_SYMBOL = Symbol.for(\"var used to be block scoped\");\n}\n\n/**\n * Check if the input `node` is a `let` variable declaration.\n */\nexport default function isLet(node: t.Node): boolean {\n  if (process.env.BABEL_8_BREAKING) {\n    return isVariableDeclaration(node) && node.kind !== \"var\";\n  } else {\n    return (\n      isVariableDeclaration(node) &&\n      (node.kind !== \"var\" ||\n        // @ts-expect-error Fixme: document private properties\n        node[BLOCK_SCOPED_SYMBOL])\n    );\n  }\n}\n"], "mappings": ";;;;;;AAAA,IAAAA,MAAA,GAAAC,OAAA;AAGmC;EAEjC,IAAIC,mBAAmB,GAAGC,MAAM,CAACC,GAAG,CAAC,6BAA6B,CAAC;AACrE;AAKe,SAASC,KAAKA,CAACC,IAAY,EAAW;EAG5C;IACL,OACE,IAAAC,4BAAqB,EAACD,IAAI,CAAC,KAC1BA,IAAI,CAACE,IAAI,KAAK,KAAK,IAElBF,IAAI,CAACJ,mBAAmB,CAAC,CAAC;EAEhC;AACF", "ignoreList": []}