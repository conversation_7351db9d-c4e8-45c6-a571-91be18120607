import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, defineComponent } from 'vue'
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 24 24'
}
export default defineComponent({
  name: 'ElectricBikeRound',
  render: function render(_ctx, _cache) {
    return (
      _openBlock(),
      _createElementBlock(
        'svg',
        _hoisted_1,
        _cache[0] ||
          (_cache[0] = [
            _createElementVNode(
              'path',
              {
                d: 'M19 7h-.82l-1.7-4.68A2.008 2.008 0 0 0 14.6 1H13c-.55 0-1 .45-1 1s.45 1 1 1h1.6l1.46 4h-4.81l-.36-1h.09c.55 0 1-.45 1-1s-.45-1-1-1H8c-.55 0-1 .45-1 1s.45 1 1 1h.75l1.82 5H9.9c-.44-2.23-2.31-3.88-4.65-3.99C2.45 6.87 0 9.2 0 12c0 2.8 2.2 5 5 5c2.46 0 4.45-1.69 4.9-4h4.2c.44 2.23 2.31 3.88 4.65 3.99c2.8.13 5.25-2.19 5.25-5C24 9.2 21.8 7 19 7zM6 13h1.82c-.42 1.23-1.6 2.08-3.02 1.99c-1.49-.09-2.73-1.35-2.8-2.85C1.93 10.39 3.27 9 5 9c1.33 0 2.42.83 2.82 2H6c-.55 0-1 .45-1 1s.45 1 1 1zm8.1-2h-1.4l-.73-2H15c-.44.58-.76 1.25-.9 2zm4.78 4c-1.54-.06-2.84-1.37-2.88-2.92c-.02-.96.39-1.8 1.05-2.36l.62 1.7c.19.52.76.79 1.28.6c.52-.19.79-.76.6-1.28l-.63-1.73l.01-.01A2.969 2.969 0 0 1 22 12c0 1.72-1.38 3.06-3.12 3zM11 20H7l6 3v-2h4l-6-3z',
                fill: 'currentColor'
              },
              null,
              -1 /* HOISTED */
            )
          ])
      )
    )
  }
})
