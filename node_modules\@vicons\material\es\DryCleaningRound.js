import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, defineComponent } from 'vue'
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 24 24'
}
export default defineComponent({
  name: 'DryCleaningRound',
  render: function render(_ctx, _cache) {
    return (
      _openBlock(),
      _createElementBlock(
        'svg',
        _hoisted_1,
        _cache[0] ||
          (_cache[0] = [
            _createElementVNode(
              'path',
              {
                d: 'M19.56 11.36L13 8.44V7c0-.55-.45-1-1-1s-1-.45-1-1s.45-1 1-1c.38 0 .72.22.88.53c.16.31.51.47.85.47c.74 0 1.26-.79.91-1.44c-.6-1.1-1.86-1.78-3.24-1.51c-1.17.23-2.12 1.2-2.34 2.37c-.29 1.56.61 2.93 1.94 3.4v.63l-6.56 2.92c-.88.38-1.44 1.25-1.44 2.2v.01C3 14.92 4.08 16 5.42 16H7v4c0 1.1.9 2 2 2h6c1.1 0 2-.9 2-2v-4h1.58c1.34 0 2.42-1.08 2.42-2.42v-.01c0-.95-.56-1.82-1.44-2.21zM18.58 14h-1.86c-.35-.6-.98-1-1.72-1H9c-.74 0-1.38.4-1.72 1H5.42c-.46 0-.58-.65-.17-.81l6.75-3l6.75 3c.42.19.28.81-.17.81z',
                fill: 'currentColor'
              },
              null,
              -1 /* HOISTED */
            )
          ])
      )
    )
  }
})
