import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, defineComponent } from 'vue'
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 24 24'
}
export default defineComponent({
  name: 'EditLocationTwotone',
  render: function render(_ctx, _cache) {
    return (
      _openBlock(),
      _createElementBlock(
        'svg',
        _hoisted_1,
        _cache[0] ||
          (_cache[0] = [
            _createElementVNode(
              'path',
              {
                opacity: '.3',
                d: 'M14.11 14H8V7.91l.59-.59L11.91 4C8.61 4.05 6 6.6 6 10.2c0 2.34 1.95 5.44 6 9.14c4.05-3.7 6-6.79 6-9.14v-.08l-3.3 3.3l-.59.58z',
                fill: 'currentColor'
              },
              null,
              -1 /* HOISTED */
            ),
            _createElementVNode(
              'path',
              {
                d: 'M18.17 4.91L17.1 3.84l-5.55 5.55v1.08h1.08l5.54-5.56zM16 2.74l1.29-1.29c.58-.59 1.52-.59 2.11-.01l.01.01l1.15 1.15c.59.59.59 1.54 0 2.12l-.68.68l-.02.02l-.58.58l-6 6H10V8.74l6-6zm-2.28-.55l-.55.55l-1.27 1.27c-3.3.05-5.9 2.6-5.9 6.2c0 2.34 1.95 5.44 6 9.14c4.05-3.7 6-6.79 6-9.14v-.1l1.8-1.8c.13.6.2 1.24.2 1.9c0 3.32-2.67 7.25-8 11.8c-5.33-4.55-8-8.48-8-11.8c0-4.98 3.8-8.2 8-8.2c.58 0 1.16.06 1.72.18z',
                fill: 'currentColor'
              },
              null,
              -1 /* HOISTED */
            ),
            _createElementVNode(
              'path',
              {
                opacity: '.3',
                d: 'M18.17 4.91L17.1 3.84l-5.55 5.55v1.08h1.08z',
                fill: 'currentColor'
              },
              null,
              -1 /* HOISTED */
            )
          ])
      )
    )
  }
})
