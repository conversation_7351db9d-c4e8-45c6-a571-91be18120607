import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, defineComponent } from 'vue'
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 24 24'
}
export default defineComponent({
  name: 'EggAltTwotone',
  render: function render(_ctx, _cache) {
    return (
      _openBlock(),
      _createElementBlock(
        'svg',
        _hoisted_1,
        _cache[0] ||
          (_cache[0] = [
            _createElementVNode(
              'path',
              {
                opacity: '.3',
                d: 'M17.59 10.42c-.69-.68-1.21-1.51-1.76-2.39C14.48 5.86 13.31 4 9.97 4c-1.62 0-2.96.52-3.98 1.55C4.68 6.88 3.97 8.99 4 11.5c.05 4.51 4.33 5.5 5.97 5.5c1.69 0 2.68 1.05 3.34 1.74c.72.76 1.19 1.26 2.68 1.26c1.89 0 4.01-2.13 4.01-4.98c0-2.2-.51-2.71-2.41-4.6zM12 15.5c-1.93 0-3.5-1.57-3.5-3.5s1.57-3.5 3.5-3.5s3.5 1.57 3.5 3.5s-1.57 3.5-3.5 3.5z',
                fill: 'currentColor'
              },
              null,
              -1 /* HOISTED */
            ),
            _createElementVNode(
              'path',
              {
                d: 'M19 9c-2-2-3.01-7-9.03-7C4.95 2 1.94 6 2 11.52C2.06 17.04 6.96 19 9.97 19c2.01 0 2.01 3 6.02 3C19 22 22 19 22 15.02C22 12 21.01 11 19 9zm-3.01 11c-1.49 0-1.96-.5-2.68-1.26c-.65-.69-1.65-1.74-3.34-1.74c-1.64 0-5.92-.99-5.97-5.5c-.03-2.51.68-4.62 1.99-5.95C7.01 4.52 8.35 4 9.97 4c3.34 0 4.51 1.86 5.86 4.02c.55.88 1.07 1.71 1.76 2.39c1.9 1.89 2.41 2.4 2.41 4.61c0 2.85-2.12 4.98-4.01 4.98z',
                fill: 'currentColor'
              },
              null,
              -1 /* HOISTED */
            ),
            _createElementVNode(
              'circle',
              {
                cx: '12',
                cy: '12',
                r: '3.5',
                fill: 'currentColor'
              },
              null,
              -1 /* HOISTED */
            )
          ])
      )
    )
  }
})
