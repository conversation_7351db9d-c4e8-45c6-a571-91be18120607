import { openBlock as _openBlock, createElementBlock as _createElementBlock, createStaticVNode as _createStaticVNode, defineComponent } from 'vue'
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 24 24'
}
export default defineComponent({
  name: 'ElectricCarTwotone',
  render: function render(_ctx, _cache) {
    return _openBlock(), _createElementBlock('svg', _hoisted_1, _cache[0] || (_cache[0] = [_createStaticVNode('<path opacity=".3" d="M5 13h14V8H5v5zm11.5-4c.83 0 1.5.67 1.5 1.5s-.67 1.5-1.5 1.5s-1.5-.67-1.5-1.5s.67-1.5 1.5-1.5zm-9 0c.83 0 1.5.67 1.5 1.5S8.33 12 7.5 12S6 11.33 6 10.5S6.67 9 7.5 9z" fill="currentColor"></path><path d="M18.92 2.01C18.72 1.42 18.16 1 17.5 1h-11c-.66 0-1.21.42-1.42 1.01L3 8v8c0 .55.45 1 1 1h1c.55 0 1-.45 1-1v-1h12v1c0 .55.45 1 1 1h1c.55 0 1-.45 1-1V8l-2.08-5.99zM6.85 3h10.29l1.08 3.11H5.77L6.85 3zM19 13H5V8h14v5z" fill="currentColor"></path><circle cx="7.5" cy="10.5" r="1.5" fill="currentColor"></circle><circle cx="16.5" cy="10.5" r="1.5" fill="currentColor"></circle><path d="M7 20h4v-2l6 3h-4v2z" fill="currentColor"></path>', 5)]))
  }
})
