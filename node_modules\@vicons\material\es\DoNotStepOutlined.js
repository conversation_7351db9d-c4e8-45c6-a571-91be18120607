import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, defineComponent } from 'vue'
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 24 24'
}
export default defineComponent({
  name: 'DoNotStepOutlined',
  render: function render(_ctx, _cache) {
    return (
      _openBlock(),
      _createElementBlock(
        'svg',
        _hoisted_1,
        _cache[0] ||
          (_cache[0] = [
            _createElementVNode(
              'path',
              {
                d: 'M18.51 15.68l-1.41-1.41l4.48-4.48L23 11.2l-4.49 4.48zm-3.53-3.53l3.07-3.07l-4.25-4.26l-3.08 3.07L9.3 6.47L13.8 2l7.08 7.08l-4.48 4.48l-1.42-1.41zm6.2 9.05l-1.41 1.41l-5.94-5.94L10.5 20H1v-2.63c0-.84.52-1.57 1.3-1.88c.58-.23 1.28-.56 1.97-1.02l1.38 1.38c.09.1.22.15.35.15s.26-.05.36-.15c.2-.2.2-.51 0-.71l-1.28-1.28c.27-.24.53-.51.77-.8l1.27 1.27a.485.485 0 0 0 .7 0c.2-.2.2-.51 0-.71l-1.4-1.4c.19-.34.34-.72.45-1.12l1.71 1.72a.485.485 0 0 0 .7 0c.19-.2.19-.5.01-.7l-7.9-7.9l1.42-1.41L21.18 21.2zm-8.76-5.94l-1.67-1.68l-3.33 3.32c-.78.78-2.05.78-2.83-.01l-.19-.17l-.47.24c-.29.14-.59.27-.89.39l-.01.65h6.64l2.75-2.74z',
                fill: 'currentColor'
              },
              null,
              -1 /* HOISTED */
            )
          ])
      )
    )
  }
})
