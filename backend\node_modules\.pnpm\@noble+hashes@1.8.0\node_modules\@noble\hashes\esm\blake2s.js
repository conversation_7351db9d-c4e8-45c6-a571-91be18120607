/**
 * Blake2s hash function. Focuses on 8-bit to 32-bit platforms. blake2b for 64-bit, but in JS it is slower.
 * @module
 * @deprecated
 */
import { G1s as G1s_n, G2s as G2s_n } from "./_blake.js";
import { SHA256_IV } from "./_md.js";
import { BLAKE2s as B2S, blake2s as b2s, compress as compress_n } from "./blake2.js";
/** @deprecated Use import from `noble/hashes/blake2` module */
export const B2S_IV = SHA256_IV;
/** @deprecated Use import from `noble/hashes/blake2` module */
export const G1s = G1s_n;
/** @deprecated Use import from `noble/hashes/blake2` module */
export const G2s = G2s_n;
/** @deprecated Use import from `noble/hashes/blake2` module */
export const compress = compress_n;
/** @deprecated Use import from `noble/hashes/blake2` module */
export const BLAKE2s = B2S;
/** @deprecated Use import from `noble/hashes/blake2` module */
export const blake2s = b2s;
//# sourceMappingURL=blake2s.js.map