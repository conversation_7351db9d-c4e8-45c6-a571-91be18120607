import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, defineComponent } from 'vue'
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 24 24'
}
export default defineComponent({
  name: 'DownloadingRound',
  render: function render(_ctx, _cache) {
    return (
      _openBlock(),
      _createElementBlock(
        'svg',
        _hoisted_1,
        _cache[0] ||
          (_cache[0] = [
            _createElementVNode(
              'path',
              {
                d: 'M17.33 3.55c-.94-.6-1.99-1.04-3.12-1.3a.998.998 0 0 0-1.21.98c0 .45.3.87.74.97c.91.2 1.77.56 2.53 1.05c.39.25.89.17 1.22-.16c.45-.45.38-1.2-.16-1.54zM20.77 11c.64 0 1.13-.59.98-1.21c-.26-1.12-.7-2.17-1.3-3.12c-.34-.54-1.1-.61-1.55-.16c-.32.32-.4.83-.16 1.22c.49.77.85 1.62 1.05 2.53a1 1 0 0 0 .98.74zm-1.87 6.49c.45.45 1.21.38 1.55-.15c.6-.94 1.04-1.99 1.3-3.11c.14-.62-.35-1.21-.98-1.21c-.45 0-.87.3-.97.74c-.2.91-.57 1.76-1.05 2.53c-.25.37-.17.88.15 1.2zM13 20.77c0 .64.59 1.13 1.21.98c1.12-.26 2.17-.7 3.11-1.3c.54-.34.61-1.1.16-1.55c-.32-.32-.83-.4-1.21-.15c-.76.49-1.61.85-2.53 1.05c-.44.1-.74.51-.74.97zM13 12V8c0-.55-.45-1-1-1s-1 .45-1 1v4H9.41c-.89 0-1.34 1.08-.71 1.71l2.59 2.59c.39.39 1.02.39 1.41 0l2.59-2.59c.63-.63.18-1.71-.71-1.71H13zm-2 8.77c0 .64-.59 1.13-1.21.99C5.33 20.75 2 16.77 2 12s3.33-8.75 7.79-9.75a.998.998 0 0 1 1.21.98c0 .46-.31.87-.76.97C6.67 5 4 8.19 4 12s2.67 7 6.24 7.8c.45.1.76.51.76.97z',
                fill: 'currentColor'
              },
              null,
              -1 /* HOISTED */
            )
          ])
      )
    )
  }
})
