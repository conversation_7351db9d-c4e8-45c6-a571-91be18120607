# CHANGELOG

### 2.0.2

- Bump to kuler 2.0, which removes colornames as dependency, which we
  never used. So smaller install size, less dependencies for all. 

### 2.0.1

- Use `storag-engine@3.0` which will automatically detect the correct
  AsyncStorage implementation.
- The upgrade also fixes a bug where it the `debug` and `diagnostics` values
  to be JSON encoded instead of regular plain text.

### 2.0.0

- Documentation improvements.
- Fixed a issue where async adapters were incorrectly detected.
- Correctly inherit colors after applying colors the browser's console.

### 2.0.0-alpha

- Complete rewrite of all internals, now comes with separate builds for `browser`
  `node` and `react-native` as well as dedicated builds for `production` and
  `development` environments. Various utility methods and properties have
  been added to the returned logger to make your lives even easier.
